from docx import Document
from docx.shared import Inches, Pt, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn
import os

def add_horizontal_line(paragraph):
    """Add a horizontal line to a paragraph"""
    p = paragraph._element
    pPr = p.get_or_add_pPr()
    pBdr = OxmlElement('w:pBdr')
    pPr.insert_element_before(pBdr, 'w:shd', 'w:tabs', 'w:suppressAutoHyphens', 'w:kinsoku', 'w:wordWrap', 'w:overflowPunct', 'w:topLinePunct', 'w:autoSpaceDE', 'w:autoSpaceDN', 'w:bidi', 'w:adjustRightInd', 'w:snapToGrid', 'w:spacing', 'w:ind', 'w:contextualSpacing', 'w:mirrorIndents', 'w:suppressOverlap', 'w:jc', 'w:textDirection', 'w:textAlignment', 'w:textboxTightWrap', 'w:outlineLvl', 'w:divId', 'w:cnfStyle', 'w:rPr', 'w:sectPr', 'w:sectPrChange')
    bottom = OxmlElement('w:bottom')
    bottom.set(qn('w:val'), 'single')
    bottom.set(qn('w:sz'), '6')
    bottom.set(qn('w:space'), '1')
    bottom.set(qn('w:color'), '2F5496')
    pBdr.append(bottom)

def create_modern_cv():
    # Create a new document
    doc = Document()
    
    # Set document margins
    sections = doc.sections
    for section in sections:
        section.top_margin = Inches(0.5)
        section.bottom_margin = Inches(0.5)
        section.left_margin = Inches(0.75)
        section.right_margin = Inches(0.75)
    
    # Header with name and contact info
    header = doc.add_paragraph()
    header.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Name
    name_run = header.add_run('SHUJA SCHADON')
    name_run.font.size = Pt(24)
    name_run.font.bold = True
    name_run.font.color.rgb = RGBColor(47, 84, 150)  # Professional blue
    
    # Contact info
    contact = doc.add_paragraph()
    contact.alignment = WD_ALIGN_PARAGRAPH.CENTER
    contact_run = contact.add_run('Rotterdam | 06-28411923 | <EMAIL>')
    contact_run.font.size = Pt(11)
    contact_run.font.color.rgb = RGBColor(89, 89, 89)
    
    # Add space
    doc.add_paragraph()
    
    # Professional Summary
    summary_heading = doc.add_paragraph()
    summary_heading_run = summary_heading.add_run('PROFESSIONEEL PROFIEL')
    summary_heading_run.font.size = Pt(14)
    summary_heading_run.font.bold = True
    summary_heading_run.font.color.rgb = RGBColor(47, 84, 150)
    add_horizontal_line(summary_heading)
    
    summary_text = doc.add_paragraph()
    summary_text.add_run('ICT-professional met uitgebreide ervaring in systeembeheer, netwerkinfrastructuur en cybersecurity. Afgestudeerd als System Devices Expert (MBO niveau 4) met praktijkervaring in firewall-configuratie, server-installaties en IT-ondersteuning. Momenteel bezig met HBO-studie en cybersecurity-certificeringen om expertise verder uit te breiden.')
    summary_text.paragraph_format.space_after = Pt(12)
    
    # Education
    education_heading = doc.add_paragraph()
    education_heading_run = education_heading.add_run('OPLEIDING & CERTIFICERINGEN')
    education_heading_run.font.size = Pt(14)
    education_heading_run.font.bold = True
    education_heading_run.font.color.rgb = RGBColor(47, 84, 150)
    add_horizontal_line(education_heading)
    
    education_items = [
        ('HBO Bedrijfskunde', 'Hogeschool Rotterdam', '2024 - heden', 'Propedeuse behaald (oktober 2025)'),
        ('MBO 4 System Devices Expert ICT', 'Techniek College Rotterdam', '2020 - 2023', 'Diploma behaald (september 2023)'),
        ('MBO 3 Medewerker ICT', 'Techniek College Rotterdam', '2018 - 2020', 'Diploma behaald (september 2020)'),
        ('VCA Basis Certificering', '', '', 'Veiligheidscertificaat voor de bouw'),
    ]
    
    for title, school, period, note in education_items:
        edu_para = doc.add_paragraph()
        edu_para.paragraph_format.left_indent = Inches(0.25)
        
        title_run = edu_para.add_run(title)
        title_run.font.bold = True
        title_run.font.size = Pt(11)
        
        if school:
            edu_para.add_run(f' | {school}')
        if period:
            edu_para.add_run(f' | {period}')
        if note:
            edu_para.add_run(f'\n{note}')
    
    doc.add_paragraph()
    
    # Professional Experience
    experience_heading = doc.add_paragraph()
    experience_heading_run = experience_heading.add_run('WERKERVARING')
    experience_heading_run.font.size = Pt(14)
    experience_heading_run.font.bold = True
    experience_heading_run.font.color.rgb = RGBColor(47, 84, 150)
    add_horizontal_line(experience_heading)
    
    # !Bien experience
    bien_para = doc.add_paragraph()
    bien_para.paragraph_format.left_indent = Inches(0.25)
    bien_title = bien_para.add_run('ICT Stagiair | !Bien')
    bien_title.font.bold = True
    bien_title.font.size = Pt(11)
    bien_para.add_run(' | Januari 2023 - Juni 2023')
    
    bien_tasks = doc.add_paragraph()
    bien_tasks.paragraph_format.left_indent = Inches(0.5)
    bien_tasks.add_run('• Firewall-installaties en -configuraties op locatie bij klanten\n')
    bien_tasks.add_run('• Automatisering van applicatie-updates via PowerShell scripts\n')
    bien_tasks.add_run('• Laptop-installaties volgens klantspecifieke procedures\n')
    bien_tasks.add_run('• Netwerkdiagrammen en technische documentatie opstellen\n')
    bien_tasks.add_run('• Router-configuratie voor remote access\n')
    bien_tasks.add_run('• Server-installaties en -configuraties\n')
    bien_tasks.add_run('• Dagelijkse backup-monitoring via RMM Datto systemen')
    
    # NMC Maritime experience
    nmc_para = doc.add_paragraph()
    nmc_para.paragraph_format.left_indent = Inches(0.25)
    nmc_title = nmc_para.add_run('Technisch Stagiair | NMC Maritime Technology')
    nmc_title.font.bold = True
    nmc_title.font.size = Pt(11)
    nmc_para.add_run(' | Mei 2014 - Mei 2015')
    
    nmc_tasks = doc.add_paragraph()
    nmc_tasks.paragraph_format.left_indent = Inches(0.5)
    nmc_tasks.add_run('• Magazijnbeheer en logistieke ondersteuning\n')
    nmc_tasks.add_run('• Technische werkzaamheden in de productie\n')
    nmc_tasks.add_run('• IT-ondersteuning en kantoororganisatie')
    
    doc.add_paragraph()
    
    # Technical Skills
    skills_heading = doc.add_paragraph()
    skills_heading_run = skills_heading.add_run('TECHNISCHE VAARDIGHEDEN')
    skills_heading_run.font.size = Pt(14)
    skills_heading_run.font.bold = True
    skills_heading_run.font.color.rgb = RGBColor(47, 84, 150)
    add_horizontal_line(skills_heading)
    
    # Operating Systems
    os_para = doc.add_paragraph()
    os_para.paragraph_format.left_indent = Inches(0.25)
    os_title = os_para.add_run('Besturingssystemen:')
    os_title.font.bold = True
    os_para.add_run(' Windows 7/10/11, macOS - Installatie, configuratie en troubleshooting')
    
    # Software
    software_para = doc.add_paragraph()
    software_para.paragraph_format.left_indent = Inches(0.25)
    software_title = software_para.add_run('Software & Tools:')
    software_title.font.bold = True
    software_para.add_run(' Microsoft Office Suite, AutoCAD 2014/2017, 3D Studio, PowerShell, RMM Datto, Sophos Firewall')
    
    # Hardware
    hardware_para = doc.add_paragraph()
    hardware_para.paragraph_format.left_indent = Inches(0.25)
    hardware_title = hardware_para.add_run('Hardware:')
    hardware_title.font.bold = True
    hardware_para.add_run(' PC-assemblage, component-installatie, hardware-troubleshooting, netwerkinfrastructuur')
    
    # Security
    security_para = doc.add_paragraph()
    security_para.paragraph_format.left_indent = Inches(0.25)
    security_title = security_para.add_run('Cybersecurity:')
    security_title.font.bold = True
    security_para.add_run(' Firewall-configuratie, backup-monitoring, security assessments')
    
    doc.add_paragraph()
    
    # Current Certifications
    cert_heading = doc.add_paragraph()
    cert_heading_run = cert_heading.add_run('LOPENDE CERTIFICERINGEN')
    cert_heading_run.font.size = Pt(14)
    cert_heading_run.font.bold = True
    cert_heading_run.font.color.rgb = RGBColor(47, 84, 150)
    add_horizontal_line(cert_heading)
    
    cert_items = [
        'The Complete Cyber Security Course: Hackers Exposed!',
        'Certified in Cybersecurity (CC) - ISC2',
        'CompTIA Security+ (SY0-601)',
        'Cyber Security: From Beginner to Expert'
    ]
    
    for cert in cert_items:
        cert_para = doc.add_paragraph()
        cert_para.paragraph_format.left_indent = Inches(0.25)
        cert_para.add_run(f'• {cert}')
    
    doc.add_paragraph()
    
    # Personal Information
    personal_heading = doc.add_paragraph()
    personal_heading_run = personal_heading.add_run('PERSOONLIJKE GEGEVENS')
    personal_heading_run.font.size = Pt(14)
    personal_heading_run.font.bold = True
    personal_heading_run.font.color.rgb = RGBColor(47, 84, 150)
    add_horizontal_line(personal_heading)
    
    personal_para = doc.add_paragraph()
    personal_para.paragraph_format.left_indent = Inches(0.25)
    personal_para.add_run('Geboortedatum: 11 april 1998\n')
    personal_para.add_run('Nationaliteit: Nederlands\n')
    personal_para.add_run('Woonplaats: Rotterdam\n')
    personal_para.add_run('Beschikbaarheid: Per direct')
    
    return doc

# Create and save the document
if __name__ == "__main__":
    cv_folder = r"C:\Users\<USER>\Documents\augment-projects\Americaps\cv-update"
    
    # Create the modern CV
    doc = create_modern_cv()
    
    # Save the document
    output_path = os.path.join(cv_folder, "CV Shuja Schadon - Modern Professional 2025.docx")
    doc.save(output_path)
    
    print(f"Moderne CV succesvol aangemaakt: {output_path}")
    print("\nKenmerken van de nieuwe CV:")
    print("✓ Moderne, professionele layout")
    print("✓ Duidelijke sectie-indeling met visuele scheiding")
    print("✓ Professionele benamingen voor alle categorieën")
    print("✓ Gestructureerde werkervaring met bullet points")
    print("✓ Technische vaardigheden georganiseerd per categorie")
    print("✓ Autolessen sectie verwijderd")
    print("✓ Talenkennis sectie verwijderd")
    print("✓ Professionele taalgebruik en formatting")
